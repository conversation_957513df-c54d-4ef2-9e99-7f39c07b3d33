package ca.bnc.payment.mapper.server.get;

import ca.bnc.payment.pmt_incoming_payment_management_resources.generated.model.CountryCode;
import ca.bnc.payment.pmt_incoming_payment_management_resources.generated.model.PartyIdentification135;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.Optional;

@Component
@RequiredArgsConstructor
public class PartyIdentification135Mapper {
    private final PostalAddress24Mapper postalAddress24Mapper;
    private final Contact4Mapper contact4Mapper;
    private final Party38ChoiceMapper party38ChoiceMapper;

    public PartyIdentification135 map(
            final ca.bnc.payment.lib.pmt_payment_interac_contract.generated.model.PartyIdentification135 partyIdentification135) {

        return new PartyIdentification135()
                .name(partyIdentification135.getName())
                .postalAddress(Optional.ofNullable(partyIdentification135.getPostalAddress())
                        .map(postalAddress24Mapper::map)
                        .orElse(null))
                .identification(Optional.ofNullable(partyIdentification135.getIdentification())
                        .map(party38ChoiceMapper::map)
                        .orElse(null))
                .countryOfResidence(Optional.ofNullable(partyIdentification135.getCountryOfResidence())
                        .map(countryCode -> CountryCode.fromValue(countryCode.getValue()))
                        .orElse(null))
                .contactDetails(Optional.ofNullable(partyIdentification135.getContactDetails())
                        .map(contact4Mapper::map)
                        .orElse(null));
    }

}
