{"FIToFIPaymentStatusReport": {"groupHeader": {"messageIdentification": "3ee01483-2b49-467c-8cde-c48d1a1fd1c0", "creationDateTime": "2019-05-05T17:29:12.123Z"}, "transactionInformationAndStatus": [{"originalTransactionReference": {"amount": {"instructedAmount": {"amount": 66.66, "currency": "CAD"}}}, "supplementaryData": {"incomingTransferInformation": {"senderName": "<PERSON>", "transferStatus": "AVAILABLE", "expiryDate": "2024-01-23T12:34:56.000Z", "senderMemo": "123TEST", "authenticationRequired": "REQUIRED", "securityQuestion": "Secret", "hashType": "SHA2", "hashSalt": "string0"}}}]}}