@get
Feature: BNC - List of unsuccessful component scenarios for GET endpoint - https://wiki.bnc.ca/x/Wa_meQ

  Scenario Outline: CTE - call IncomingPayment but Get Remittance from Interac Payments replies with error
    Given a request method "GET" and URI equal "/management-incomingDomesticPayment/%s"
    And the request has headers "bnc/payloads/get/input/CTE/CTE_header.json"
    And the request has ETransferId "CAq7Q5ww"
    And service calls "registration" API with these headers "bnc/payloads/get/input/registration/input/CTE/CTE_header.json" with body "nothing"
    And "registration" API response is "200" with body "bnc/payloads/get/input/registration/output/CTE/CTE_body.json" to the "GET" method
    And service calls "ppam" API with these headers "bnc/payloads/get/input/ppam/input/CTE/CTE_header.json" with body "nothing"
    And "ppam" API response is "200" with body "bnc/payloads/get/input/ppam/output/CTE/CTE_response.json" to the "GET" method
    And service calls "party" API with these headers "bnc/payloads/get/input/party/input/CTE/CTE_header.json" with body "nothing"
    And service calls party API with this client id "30BD2E5746C6013778F4CD84D4533FAE6C2F452D0"
    And "party" API response is "200" with body "bnc/payloads/get/input/party/output/CTE/CTE_body.json" to the "GET" method
    And a interac token manager for instant "2019-05-05T17:29:12.123Z" that returns "12345"
    And service calls "interac-incoming" API with these headers "bnc/payloads/get/input/interac/input/CTE/CTE_header.json" with body "nothing"
    And "interac-incoming" API response is "200" with body "bnc/payloads/get/input/interac/output/CTE/CTE_body.json" to the "GET" method
    And service calls "interac-remittance" API with these headers "bnc/payloads/get/input/interac/input/CTE/CTE_header.json" with body "nothing"
    And "interac-remittance" API response is "<remittance_response>" with body "<remittance_body>" to the "GET" method
    When the request is processed
    Then the server response status is "200" with body "bnc/payloads/get/output/CTE/CTE_body.json"
    And dynamodb etransfer should match "bnc/payloads/get/output/dynamodb/CTE.json"
    And verify <remittance_calls> times "interac-remittance" API is called without body to the "GET" method
    And the log message for Get operation in "bnc/payloads/get/logging/" contains information from file
      | <logging> |
    Examples:
      | remittance_response | remittance_body                                         | remittance_calls | logging                  |
      | time out            | nothing                                                 | 4                | CTE/CTE1_service_log.txt |
      | 400                 | bnc/payloads/get/input/interac/input/CTE/CTE2_body.json | 1                | CTE/CTE2_service_log.txt |
      | 401                 | nothing                                                 | 1                | CTE/CTE3_service_log.txt |
      | 403                 | nothing                                                 | 1                | CTE/CTE4_service_log.txt |
      | 404                 | bnc/payloads/get/input/interac/input/CTE/CTE5_body.json | 1                | CTE/CTE5_service_log.txt |
      | 429                 | nothing                                                 | 1                | CTE/CTE6_service_log.txt |
      | 500                 | nothing                                                 | 1                | CTE/CTE7_service_log.txt |
      | 503                 | bnc/payloads/get/input/interac/input/CTE/CTE8_body.json | 4                | CTE/CTE8_service_log.txt |
