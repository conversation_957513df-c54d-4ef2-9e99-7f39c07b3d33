@get
Feature: BNC - List of unsuccessful component scenarios for GET endpoint - https://wiki.bnc.ca/x/-BPshw

  Scenario Outline: <CTF> - call GET IncomingPayment but <scenario>
    Given a request method "GET" and URI equal "/management-incomingDomesticPayment/%s"
    And the request has headers "bnc/payloads/get/input/CTF/CTF_header.json"
    And the request has ETransferId "CAq7Q5ww"
    And service calls "registration" API with these headers "bnc/payloads/get/input/registration/input/CTF/CTF_header.json" with body "nothing"
    And "registration" API response is "200" with body "bnc/payloads/get/input/registration/output/CTF/CTF_body.json" to the "GET" method
    And service calls "ppam" API with these headers "bnc/payloads/get/input/ppam/input/CTF/CTF_header.json" with body "nothing"
    And "ppam" API response is "<ppam_response>" with body "<ppam_response_body>" to the "GET" method
    When the request is processed
    Then the server response status is "<service_response>" with body "<get_response>"
    And verify 1 times "registration" API is called without body to the "GET" method
    And verify <count> times "ppam" API is called without body to the "GET" method
    And the log message for Get operation in "bnc/payloads/get/logging/" contains information from file
      | <service_logs> |
    Examples:
      | CTF  | ppam_response | ppam_response_body                                        | get_response                               | service_logs             | service_response | count |
      | CTF1 | time out      | nothing                                                   | bnc/payloads/get/output/CTF/CTF1_body.json | CTF/CTF1_service_log.txt | 500              | 1     |
      | CTF2 | 400           | bnc/payloads/get/input/ppam/output/CTF/CTF2_response.json | bnc/payloads/get/output/CTF/CTF2_body.json | CTF/CTF2_service_log.txt | 500              | 1     |
      | CTF3 | 404           | bnc/payloads/get/input/ppam/output/CTF/CTF3_response.json | bnc/payloads/get/output/CTF/CTF3_body.json | CTF/CTF3_service_log.txt | 400              | 1     |
      | CTF4 | 500           | bnc/payloads/get/input/ppam/output/CTF/CTF4_response.json | bnc/payloads/get/output/CTF/CTF4_body.json | CTF/CTF4_service_log.txt | 500              | 1     |
      | CTF5 | 406           | bnc/payloads/get/input/ppam/output/CTF/CTF5_response.json | bnc/payloads/get/output/CTF/CTF5_body.json | CTF/CTF5_service_log.txt | 500              | 1     |