@get
Feature: BNC - List of unsuccessful component scenarios for GET endpoint - https://wiki.bnc.ca/x/Va_meQ

  Scenario Outline: <CTA> - call GET IncomingPayment but <scenario>
    Given a request method "GET" and URI equal "/management-incomingDomesticPayment/%s"
    And the request has headers "bnc/payloads/get/input/CTA/CTA_header.json"
    And the request has ETransferId "CAq7Q5ww"
    And service calls "registration" API with these headers "bnc/payloads/get/input/registration/input/CTA/CTA_header.json" with body "nothing"
    And "registration" API response is "<registration_status>" with body "<registration_body>" to the "GET" method
    When the request is processed
    Then the server response status is "<response_status>" with body "<response_body>"
    And verify <count> times "registration" API is called without body to the "GET" method
    And the log message for Get operation in "bnc/payloads/get/logging/" contains information from file
      | <service_log> |
    Examples:
      | CTA  | registration_status | registration_body                                             | response_status | response_body                              | count | service_log              |
      | CTA1 | time out            | nothing                                                       | 500             | bnc/payloads/get/output/CTA/CTA1_body.json | 1     | CTA/CTA1_service_log.txt |
      | CTA2 | 400                 | bnc/payloads/get/input/registration/output/CTA/CTA2_body.json | 500             | bnc/payloads/get/output/CTA/CTA2_body.json | 1     | CTA/CTA2_service_log.txt |
      | CTA3 | 404                 | nothing                                                       | 400             | bnc/payloads/get/output/CTA/CTA3_body.json | 1     | CTA/CTA3_service_log.txt |
      | CTA4 | 500                 | bnc/payloads/get/input/registration/output/CTA/CTA4_body.json | 500             | bnc/payloads/get/output/CTA/CTA4_body.json | 1     | CTA/CTA4_service_log.txt |
      | CTA5 | 503                 | bnc/payloads/get/input/registration/output/CTA/CTA5_body.json | 500             | bnc/payloads/get/output/CTA/CTA5_body.json | 4     | CTA/CTA5_service_log.txt |

  Scenario: CTAT - call GET IncomingPayment but pmt-partners-registration-reader-api replies with a HTTP-500 error with no payload to deserialize
    Given a request method "GET" and URI equal "/management-incomingDomesticPayment/%s"
    And the request has headers "bnc/payloads/get/input/CTA/CTA_header.json"
    And the request has ETransferId "CAq7Q5ww"
    And service calls "registration" API with these headers "bnc/payloads/get/input/registration/input/CTA/CTAT_header.json" with body "nothing"
    And "registration" API response is "500" with body "nothing" to the "GET" method
    When the request is processed
    Then the server response status is "500" with body "bnc/payloads/get/output/CTA/CTAT_body.json"
    And verify 1 times "registration" API is called without body to the "GET" method
