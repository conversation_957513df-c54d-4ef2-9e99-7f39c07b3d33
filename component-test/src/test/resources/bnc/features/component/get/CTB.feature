@get
Feature: BNC - List of unsuccessful component scenarios for GET endpoint - https://wiki.bnc.ca/x/Vq_meQ

  Scenario Outline: <CTB> - call GET IncomingPayment
    Given a request method "GET" and URI equal "/management-incomingDomesticPayment/%s"
    And the request has headers "bnc/payloads/get/input/CTB/CTB_header.json"
    And the request has ETransferId "CAq7Q5ww"
    And service calls "registration" API with these headers "bnc/payloads/get/input/registration/input/CTB/CTB_header.json" with body "nothing"
    And "registration" API response is "200" with body "bnc/payloads/get/input/registration/output/CTB/CTB_body.json" to the "GET" method
    And service calls "ppam" API with these headers "bnc/payloads/get/input/ppam/input/CTB/CTB_header.json" with body "nothing"
    And "ppam" API response is "200" with body "bnc/payloads/get/input/ppam/output/CTB/CTB_body.json" to the "GET" method
    And service calls "party" API with these headers "bnc/payloads/get/input/party/input/CTB/CTB_header.json" with body "nothing"
    And service calls party API with this client id "30BD2E5746C6013778F4CD84D4533FAE6C2F452D0"
    And "party" API response is "<party_response>" with body "<party_response_body>" to the "GET" method
    When the request is processed
    Then the server response status is "<service_response>" with body "<get_response>"
    And verify 1 times "registration" API is called without body to the "GET" method
    And verify 1 times "ppam" API is called without body to the "GET" method
    And verify 1 times "party" API is called without body to the "GET" method
    And the log message for Get operation in "bnc/payloads/get/logging/" contains information from file
      | <service_logs> |
    Examples:
      | CTB  | party_response | party_response_body                                    | get_response                               | service_logs             | service_response |
      | CTB1 | time out       | nothing                                                | bnc/payloads/get/output/CTB/CTB1_body.json | CTB/CTB1_service_log.txt | 500              |
      | CTB2 | 400            | bnc/payloads/get/input/party/output/CTB/CTB2_body.json | bnc/payloads/get/output/CTB/CTB2_body.json | CTB/CTB2_service_log.txt | 500              |
      | CTB3 | 500            | bnc/payloads/get/input/party/output/CTB/CTB3_body.json | bnc/payloads/get/output/CTB/CTB3_body.json | CTB/CTB3_service_log.txt | 500              |
