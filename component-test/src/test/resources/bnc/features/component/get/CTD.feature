@get
Feature: BNC - List of unsuccessful component scenarios for GET endpoint - https://wiki.bnc.ca/x/WK_meQ

  Scenario Outline: <CTD> - call IncomingPayment but Put Item operation from AWS Dynamo DB API does not reply
    Given a request method "GET" and URI equal "/management-incomingDomesticPayment/%s"
    And the request has headers "bnc/payloads/get/input/CTD/CTD_header.json"
    And the request has ETransferId "CAq7Q5ww"
    And service calls "registration" API with these headers "bnc/payloads/get/input/registration/input/CTD/CTD_header.json" with body "nothing"
    And "registration" API response is "200" with body "common/payloads/get/input/registration/output/CTD/CTD_body.json" to the "GET" method
    And service calls "ppam" API with these headers "bnc/payloads/get/input/ppam/input/CTD/CTD_header.json" with body "nothing"
    And "ppam" API response is "200" with body "bnc/payloads/get/input/ppam/output/CTD/CTD_body.json" to the "GET" method
    And service calls "party" API with these headers "bnc/payloads/get/input/party/input/CTD/CTD_header.json" with body "nothing"
    And service calls party API with this client id "30BD2E5746C6013778F4CD84D4533FAE6C2F452D0"
    And "party" API response is "200" with body "common/payloads/get/input/party/output/CTD/CTD_body.json" to the "GET" method
    And a interac token manager for instant "2019-05-05T17:29:12.123Z" that returns "12345"
    And service calls "interac-incoming" API with these headers "bnc/payloads/get/input/interac/input/CTD/CTD_header.json" with body "nothing"
    And "interac-incoming" API response is "200" with body "common/payloads/get/input/interac/output/CTD/CTD_body.json" to the "GET" method
    And DynamoDB respond with "<dynamodb_status>" for "put" operation
    When the request is processed
    Then the server response status is "<service_status>" with body "<service_response>"
    And verify 1 times "registration" API is called without body to the "GET" method
    And verify 1 times "ppam" API is called without body to the "GET" method
    And verify 1 times "party" API is called without body to the "GET" method
    And verify 1 times "interac-incoming" API is called without body to the "GET" method
    And the log message for Get operation in "common/payloads/get/logging/" contains information from file
      | <service_log> |
    Examples:
      | CTD  | dynamodb_status | service_status | service_response                              | service_log              |
      | CTD1 | a timeout       | 500            | common/payloads/get/output/CTD/CTD1_body.json | CTD/CTD1_service_log.txt |
      | CTD2 | 400             | 500            | common/payloads/get/output/CTD/CTD2_body.json | CTD/CTD2_service_log.txt |
      | CTD3 | 500             | 500            | common/payloads/get/output/CTD/CTD3_body.json | CTD/CTD3_service_log.txt |