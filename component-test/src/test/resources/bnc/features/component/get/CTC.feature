@get
Feature: BNC - List of unsuccessful component scenarios for GET endpoint - https://wiki.bnc.ca/x/V6_meQ

  Scenario Outline: <CTC> - call GET IncomingPayment but pmt-payment-interac-api does not reply
    Given a request method "GET" and URI equal "/management-incomingDomesticPayment/%s"
    And the request has headers "bnc/payloads/get/input/CTC/CTC_header.json"
    And the request has ETransferId "CAq7Q5ww"
    And service calls "registration" API with these headers "bnc/payloads/get/input/registration/input/CTC/CTC_header.json" with body "nothing"
    And "registration" API response is "200" with body "common/payloads/get/input/registration/output/CTC/CTC_body.json" to the "GET" method
    And service calls "ppam" API with these headers "bnc/payloads/get/input/ppam/input/CTC/CTC_header.json" with body "nothing"
    And "ppam" API response is "200" with body "bnc/payloads/get/input/ppam/output/CTC/CTC_response.json" to the "GET" method
    And service calls "party" API with these headers "bnc/payloads/get/input/party/input/CTC/CTC_header.json" with body "nothing"
    And service calls party API with this client id "30BD2E5746C6013778F4CD84D4533FAE6C2F452D0"
    And "party" API response is "200" with body "common/payloads/get/input/party/output/CTC/CTC_body.json" to the "GET" method
    And a interac token manager for instant "2019-05-05T17:29:12.123Z" that returns "12345"
    And service calls "interac-incoming" API with these headers "bnc/payloads/get/input/interac/input/CTC/CTC_header.json" with body "nothing"
    And "interac-incoming" API response is "<interac_status>" with body "<interac_response>" to the "GET" method
    When the request is processed
    Then the server response status is "<service_status>" with body "<service_response>"
    And verify 1 times "registration" API is called without body to the "GET" method
    And verify 1 times "ppam" API is called without body to the "GET" method
    And verify 1 times "party" API is called without body to the "GET" method
    And verify <nb_interac_calls> times "interac-incoming" API is called without body to the "GET" method
    And the log message for Get operation in "common/payloads/get/logging/" contains information from file
      | <service_log> |

    Examples:
      | CTC  | interac_status | interac_response                                               | nb_interac_calls | service_status | service_response                              | service_log              |
      | CTC1 | time out       | nothing                                                        | 4                | 500            | common/payloads/get/output/CTC/CTC1_body.json | CTC/CTC1_service_log.txt |
      | CTC2 | 400            | common/payloads/get/input/interac/output/CTC/CTC-999_body.json | 1                | 500            | common/payloads/get/output/CTC/CTC2_body.json | CTC/CTC2_service_log.txt |
      | CTC3 | 400            | common/payloads/get/input/interac/output/CTC/CTC-301_body.json | 1                | 400            | common/payloads/get/output/CTC/CTC3_body.json | CTC/CTC3_service_log.txt |
      | CTC4 | 401            | nothing                                                        | 1                | 500            | common/payloads/get/output/CTC/CTC4_body.json | CTC/CTC4_service_log.txt |
      | CTC5 | 403            | nothing                                                        | 1                | 500            | common/payloads/get/output/CTC/CTC5_body.json | CTC/CTC5_service_log.txt |
      | CTC6 | 404            | common/payloads/get/input/interac/output/CTC/CTC-999_body.json | 1                | 404            | nothing                                       | CTC/CTC6_service_log.txt |
      | CTC7 | 429            | nothing                                                        | 1                | 500            | common/payloads/get/output/CTC/CTC7_body.json | CTC/CTC7_service_log.txt |
      | CTC8 | 500            | nothing                                                        | 1                | 500            | common/payloads/get/output/CTC/CTC8_body.json | CTC/CTC8_service_log.txt |
      | CTC9 | 503            | common/payloads/get/input/interac/output/CTC/CTC-999_body.json | 4                | 500            | common/payloads/get/output/CTC/CTC9_body.json | CTC/CTC9_service_log.txt |
      | CTCT | 404            | nothing                                                        | 1                | 500            | common/payloads/get/output/CTC/CTCT_body.json | CTC/CTCT_service_log.txt |

