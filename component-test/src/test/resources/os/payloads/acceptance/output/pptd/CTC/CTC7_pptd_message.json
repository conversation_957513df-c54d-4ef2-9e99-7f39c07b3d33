{"version": "1.0.0", "entity": "DOMESTIC_ETRANSFER_PAYMENT", "eventType": "PAYMENT-TRANSACTION-DATA-EXPORTED", "eventTime": "2019-05-05T17:29:12.123Z", "instructionIdentification": "379CD1CD9A284A2A8F8A7B5A28499070", "endToEndBusinessIdentification": "a098bdb2-545e-4f0a-9055-a4372-01", "channelId": "OSFIN", "channelType": "WEB", "clientId": "30BD2E5746C6013778F4CD84D4533FAE6C2F452D0", "clientType": "I", "paymentTypeCode": "QA", "msgDefIdr": "pacs.008.001.08", "rawData": {"instructionIdentification": "379CD1CD9A284A2A8F8A7B5A28499070", "endToEndBusinessIdentification": "a098bdb2-545e-4f0a-9055-a4372-01", "approvalRequired": false, "messageDefinitionIdentifier": "pacs.008.001.08", "clientId": "30BD2E5746C6013778F4CD84D4533FAE6C2F452D0", "creationDatetime": "2019-05-05T17:29:12.123Z", "messageData": {"fiToFiCustomerCreditTransfer": {"group_header": {"message_identification": "d04273e9014645c2b12e3ef18ef8589c", "creation_datetime": "2019-05-05T17:29:12.123Z", "number_of_transactions": "1"}, "credit_transfer_transaction_information": [{"payment_identification": {}, "payment_type_information": {"local_instrument": {"proprietary": "REGULAR_PAYMENT"}}, "interbank_settlement_amount": {"amount": 66.66, "currency": "CAD"}, "interbank_settlement_date": "2019-05-05", "debtor": {"name": "<PERSON>"}, "creditor": {"identification": {"organisation_identification": {"other": [{"identification": "30BD2E5746C6013778F4CD84D4533FAE6C2F452D0"}]}}}, "creditor_account": {"identification": {"other": {"identification": "612-12345-************"}}}}]}}, "supplementaryData": {"paymentDirection": "IN", "transactionStatus": "RJCT", "statusReasonInformation": {"code": "TECHNICAL_ERROR", "proprietary": "The Interac Payments API encountered an unexpected condition that prevented it from fulfilling the Complete Transfer Begin request"}, "eTransferId": "CAq7Q5ww", "accountHolderName": "<PERSON>", "interacUserId": "CA000612-user-240"}}}