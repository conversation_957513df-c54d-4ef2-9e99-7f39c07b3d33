{"errors": [{"code": "REQUEST_INVALID", "text": "Validation failed for argument [8] in public org.springframework.http.ResponseEntity<java.lang.Void> ca.bnc.payment.controller.IncomingPaymentManagementApi.authentication(java.lang.String,java.lang.String,ca.bnc.payment.pmt_incoming_payment_management_resources.generated.model.ChannelType,java.lang.String,java.lang.String,java.util.UUID,java.util.UUID,java.lang.String,ca.bnc.payment.pmt_incoming_payment_management_resources.generated.model.AuthenticateRequest,java.lang.String): [Field error in object 'authenticateRequest' on field 'fiToFICustomerCreditTransfer.groupHeader': rejected value [null]; codes [NotNull.authenticateRequest.fiToFICustomerCreditTransfer.groupHeader,NotNull.fiToFICustomerCreditTransfer.groupHeader,NotNull.groupHeader,NotNull.ca.bnc.payment.pmt_incoming_payment_management_resources.generated.model.GroupHeader93,NotNull]; arguments [org.springframework.context.support.DefaultMessageSourceResolvable: codes [authenticateRequest.fiToFICustomerCreditTransfer.groupHeader,fiToFICustomerCreditTransfer.groupHeader]; arguments []; default message [fiToFICustomerCreditTransfer.groupHeader]]; default message [must not be null]] ", "origin": "pmt-incoming-payment-management", "rule": "NA"}]}