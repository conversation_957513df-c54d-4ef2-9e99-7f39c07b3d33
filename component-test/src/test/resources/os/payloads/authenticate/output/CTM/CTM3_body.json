{"errors": [{"code": "REQUEST_INVALID", "text": "Required request body is missing: public org.springframework.http.ResponseEntity<java.lang.Void> ca.bnc.payment.controller.IncomingPaymentManagementApi.authentication(java.lang.String,java.lang.String,ca.bnc.payment.pmt_incoming_payment_management_resources.generated.model.ChannelType,java.lang.String,java.lang.String,java.util.UUID,java.util.UUID,java.lang.String,ca.bnc.payment.pmt_incoming_payment_management_resources.generated.model.AuthenticateRequest,java.lang.String)", "origin": "pmt-incoming-payment-management", "rule": "NA"}]}