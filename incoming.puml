@startuml
' See https://github.com/awslabs/aws-icons-for-plantuml/blob/main/AWSSymbols.md
!define AWSPuml https://raw.githubusercontent.com/awslabs/aws-icons-for-plantuml/v15.0/dist
!include AWSPuml/AWSCommon.puml
!include AWSPuml/Compute/all.puml
!include AWSPuml/Database/all.puml
!include  AWSPuml/ApplicationIntegration/all.puml
!include  AWSPuml/Containers/all.puml

skinparam wrapWidth 0
skinparam maxMessageSize 0


' avoid problems with angled crows feet
skinparam linetype ortho
skinparam Shadowing false
skinparam defaulttextalignment center
skinparam SequenceMessageAlign center
skinparam SequenceGroupBodyBackgroundColor transparent



skinparam sequencebox {
    BorderColor #grey
    BackGroundColor #WhiteSmoke
}

skinparam participant {
    BackgroundColor #white
    BorderColor #black
}

skinparam sequence {
LifeLineBorderColor #black
}
skinparam sequenceReferenceBackgroundColor #palegreen
skinparam sequenceReferenceHeaderBackgroundColor #limegreen
skinparam sequenceGroupBackgroundColor #skyblue
skinparam sequenceDividerBackgroundColor #yellow

skinparam Arrow {
    Thickness 2
    Color black
    FontColor black
}
skinparam style strictuml




box "PYMT Domain - Domestic Payment" #E1D5E7
  participant "==$AWSImg(ElasticContainerServiceContainer2)\npmt-incoming-domestic-payment-api" as etransferBusinessAPI
end box

box "PYMT Domain - Technical API" #FFE6CC
  participant "==$AWSImg(ElasticContainerServiceContainer2)\n**pmt-incoming-domestic-payment-management**\n//(Main thread)//" as etransferManagementAPI
  control "//:working_thread//" as thread #powderblue

  participant "//DynamoDB Global Table//\n==$AWSImg(DynamoDB)\nINCOMING_DOMESTIC_PAYMENT_TABLE" as db

  participant "==$AWSImg(ElasticContainerServiceContainer2)\npmt-domestic-payment-bank-account-connector" as accountConnectorAPI
end box

box "PYMT Domain" #E1D5E7

 participant  "Kafka Topic\n==$AWSImg(SimpleNotificationServiceTopic)\n**pptd.prd.tpc.etransfer.pmt.trx.v1**" as pptd

end box

box "Interac" #E6E6E6
  participant "<$ElasticContainerServiceContainer2>\nPayments REST API" as interac
end box



'box "Temenos" #E6E6E6
 ' participant "<$ElasticContainerServiceContainer2>\nCore Banking API" as temenos
'end box




    etransferBusinessAPI -> etransferManagementAPI:  call **Accept Payment** request\n**<color:green>($AcceptPaymentPayload)</color>**\n**<color:blue>(R1)</color>**
    etransferManagementAPI -> etransferManagementAPI : **Validate** if the request\n respect the contract **<color:blue>(R2)</color>**
    alt Validation Failed
        etransferManagementAPI --> etransferBusinessAPI : <color:red> **HTTP-400**</color> **<color:red>(ER2a)</color>**
    end
	etransferManagementAPI -> etransferManagementAPI : **Validate** if the callback\n is whitelisted **<color:blue>(R3)</color>**
	alt CallBack URL invalid
        etransferManagementAPI -> etransferBusinessAPI :  <color:red> **HTTP-400**</color> **<color:red>(ER3a)</color>**
    end
	group try
    etransferManagementAPI -> thread **: //create and start new thread// **<color:blue> (R4)</color>**
      alt ON ERROR **<color:red>(ER4a)</color>**
        etransferManagementAPI --> etransferBusinessAPI : <color:red> **HTTP-500**</color> **<color:red>(ER4a)</color>**
      end
    end
    |||
	par
	'etransferManagementAPI -> etransferManagementAPI: Prepare the response for the sender application **<color:blue>(R5.1)</color>**
	etransferManagementAPI --> etransferBusinessAPI: **<color:green>HTTP-202</color>** **<color:blue>(R5)</color>**
	else
	ref over etransferManagementAPI, db
        **Retrieve all payment information <color:blue>(RA.*)</color>**
    end ref
    alt <color:red>Call Initiate credit when payment is OONA</color>
        etransferManagementAPI -> accountConnectorAPI: **Initiate the bank account credit <color:blue>(RB.*)</color>**
    end
    ref over etransferManagementAPI, interac
        **Receive Payment Begin Request <color:blue>(RC.*)</color>**
    end ref
    ref over etransferManagementAPI, accountConnectorAPI
        **Confirm the bank account credit <color:blue>(RD.*)</color>**
    end ref

    ref over etransferManagementAPI, interac
        **Receive Payment Commit Request <color:blue>(RE.*)</color>**
    end ref
	thread -> thread !!: **Terminate**
    end

ref over etransferManagementAPI, pptd
**Publish to pptd topic**  **<color:blue>(RF.*)</color>**
end ref

@enduml